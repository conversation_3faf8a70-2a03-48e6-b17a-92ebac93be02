# SSO 登录功能说明

本项目已集成 Authelia SSO 登录功能，支持在生产环境启用认证，开发环境跳过认证。

## 功能特性

- **环境控制**: 通过环境变量控制是否启用SSO登录
- **开发友好**: 开发环境默认跳过认证，便于本地开发
- **生产安全**: 生产环境强制要求SSO认证
- **自动令牌管理**: 自动处理访问令牌的获取和刷新
- **用户信息显示**: 在导航栏显示用户信息和登出功能

## 环境配置

### 开发环境 (.env)
```bash
REACT_APP_SSO_ENABLED=false # 跳过SSO登录
AUTHELIA_WELL_KNOWN_URL=https://sso.01sworld.top:8443/.well-known/openid-configuration
AUTHELIA_CLIENT_ID=aiworkspace
```

### 生产环境 (.env.production)
```bash
REACT_APP_SSO_ENABLED=true # 启用SSO登录
AUTHELIA_WELL_KNOWN_URL=https://sso.01sworld.top:8443/.well-known/openid-configuration
AUTHELIA_CLIENT_ID=aiworkspace
```

## 核心组件

### 1. AuthService (`src/services/auth.ts`)
- 管理OIDC客户端配置
- 处理登录、登出、令牌刷新
- 提供认证状态检查

### 2. AuthContext (`src/contexts/AuthContext.tsx`)
- 全局认证状态管理
- 提供认证相关的React Hooks

### 3. ProtectedRoute (`src/components/ProtectedRoute.tsx`)
- 路由保护组件
- 未认证用户自动跳转到登录页

### 4. Login (`src/components/Login.tsx`)
- 登录页面组件
- 处理SSO登录流程

### 5. AuthCallback (`src/components/AuthCallback.tsx`)
- 处理SSO登录回调
- 路由: `/auth/callback`

### 6. SilentCallback (`src/components/SilentCallback.tsx`)
- 处理静默令牌刷新
- 路由: `/auth/silent-callback`

## API 集成

API 客户端已自动集成认证功能：
- 自动在请求头添加 `Authorization: Bearer <token>`
- 处理401认证错误，自动重新登录
- 支持令牌自动刷新

## 使用方式

### 开发环境
1. 确保 `.env` 文件中 `REACT_APP_SSO_ENABLED=false`
2. 启动应用: `npm start`
3. 应用将跳过认证，直接进入主界面

### 生产环境
1. 确保 `.env.production` 文件中 `REACT_APP_SSO_ENABLED=true`
2. 构建应用: `npm run build`
3. 用户访问时将被重定向到Authelia登录页面
4. 登录成功后返回应用主界面

## 路由说明

- `/` - 主应用界面（需要认证）
- `/auth/callback` - SSO登录回调处理
- `/auth/silent-callback` - 静默令牌刷新处理

## 注意事项

1. **Authelia配置**: 确保Authelia服务器已正确配置客户端ID和回调URL
2. **HTTPS要求**: 生产环境建议使用HTTPS
3. **令牌存储**: 令牌存储在localStorage中，注意安全性
4. **跨域配置**: 确保后端API支持CORS并接受Authorization头

## 故障排除

### 常见问题

1. **登录失败**
   - 检查Authelia服务器是否可访问
   - 验证客户端ID配置是否正确
   - 查看浏览器控制台错误信息

2. **令牌过期**
   - 应用会自动尝试刷新令牌
   - 如果刷新失败，会重新跳转到登录页

3. **开发环境认证问题**
   - 确认 `REACT_APP_SSO_ENABLED=false`
   - 重启开发服务器使环境变量生效

### 调试模式

在浏览器控制台中可以查看认证相关的日志信息，包括：
- 用户登录状态
- 令牌获取和刷新
- API请求认证头

## 扩展功能

如需扩展SSO功能，可以考虑：
- 添加角色权限控制
- 集成更多用户信息字段
- 支持多租户认证
- 添加审计日志功能

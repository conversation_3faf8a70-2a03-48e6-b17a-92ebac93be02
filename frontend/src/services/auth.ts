import { UserManager, User, WebStorageStateStore } from 'oidc-client-ts';

// 从环境变量获取配置
const getAuthConfig = () => {
  const ssoEnabled = process.env.REACT_APP_SSO_ENABLED === 'true';
  const wellKnownUrl = process.env.AUTHELIA_WELL_KNOWN_URL || '';
  const clientId = process.env.AUTHELIA_CLIENT_ID || '';
  const redirectUri = `${process.env.NEXTAUTH_URL}/auth/callback`;
  const postLogoutRedirectUri = window.location.origin;

  return {
    ssoEnabled,
    wellKnownUrl,
    clientId,
    redirectUri,
    postLogoutRedirectUri,
  };
};

// OIDC 用户管理器配置
const createUserManager = () => {
  const config = getAuthConfig();
  
  if (!config.ssoEnabled) {
    return null;
  }

  return new UserManager({
    authority: config.wellKnownUrl.replace('/.well-known/openid-configuration', ''),
    client_id: config.clientId,
    redirect_uri: config.redirectUri,
    post_logout_redirect_uri: config.postLogoutRedirectUri,
    response_type: 'code',
    scope: 'openid profile email',
    automaticSilentRenew: true,
    silent_redirect_uri: `${window.location.origin}/auth/silent-callback`,
    /**
     * token/session存储位置
     * 优先建议后端设置httpOnly cookie（前端无法主动设置），如后端暂不支持，为降低XSS风险，前端仅用sessionStorage存储token，避免localStorage持久暴露。
     * 若后续后端支持httpOnly cookie，请移除本地存储相关逻辑。
     * XSS防护建议：所有token相关操作务必严格校验输入、避免任何DOM注入、定期安全扫描。
     */
    userStore: new WebStorageStateStore({ store: window.sessionStorage }),
  });
};

class AuthService {
  private userManager: UserManager | null;
  private user: User | null = null;
  private config = getAuthConfig();

  constructor() {
    this.userManager = createUserManager();
    this.init();
  }

  private async init() {
    if (!this.config.ssoEnabled || !this.userManager) {
      return;
    }

    try {
      this.user = await this.userManager.getUser();
    } catch (error) {
      console.error('Failed to get user:', error);
    }
  }

  // 检查是否启用SSO
  public isSSOEnabled(): boolean {
    return this.config.ssoEnabled;
  }

  // 获取当前用户
  public async getUser(): Promise<User | null> {
    if (!this.config.ssoEnabled || !this.userManager) {
      return null;
    }

    if (!this.user) {
      try {
        this.user = await this.userManager.getUser();
      } catch (error) {
        console.error('Failed to get user:', error);
        return null;
      }
    }

    return this.user;
  }

  // 检查用户是否已认证
  public async isAuthenticated(): Promise<boolean> {
    if (!this.config.ssoEnabled) {
      return true; // 开发环境跳过认证
    }

    const user = await this.getUser();
    return user !== null && !user.expired;
  }

  // 登录
  public async login(): Promise<void> {
    if (!this.config.ssoEnabled || !this.userManager) {
      return;
    }

    try {
      await this.userManager.signinRedirect();
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  // 处理登录回调
  public async handleCallback(): Promise<User | null> {
    if (!this.config.ssoEnabled || !this.userManager) {
      return null;
    }

    try {
      this.user = await this.userManager.signinRedirectCallback();
      return this.user;
    } catch (error) {
      console.error('Callback handling failed:', error);
      throw error;
    }
  }

  // 静默续期回调
  public async handleSilentCallback(): Promise<void> {
    if (!this.config.ssoEnabled || !this.userManager) {
      return;
    }

    try {
      await this.userManager.signinSilentCallback();
    } catch (error) {
      console.error('Silent callback failed:', error);
      throw error;
    }
  }

  // 登出
  public async logout(): Promise<void> {
    if (!this.config.ssoEnabled || !this.userManager) {
      return;
    }

    try {
      // 清理内存中的用户信息
      this.user = null;
      // 防御性清理 sessionStorage 和 localStorage，防止 token/session 残留
      window.sessionStorage.clear();
      window.localStorage.clear();
      // 清理 userManager 可能的缓存（如有）
      if (this.userManager && typeof this.userManager.clearStaleState === 'function') {
        await this.userManager.clearStaleState();
      }
      await this.userManager.signoutRedirect();
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
    // [安全日志] 登出流程已显式清理 sessionStorage、localStorage 及 userManager 缓存，降低 token/session 残留风险
  }

  // 获取访问令牌
  public async getAccessToken(): Promise<string | null> {
    if (!this.config.ssoEnabled) {
      return null;
    }

    const user = await this.getUser();
    return user?.access_token || null;
  }

  // 清除用户信息
  public clearUser(): void {
    this.user = null;
  }
}

// 导出单例实例
export const authService = new AuthService();
export default authService;

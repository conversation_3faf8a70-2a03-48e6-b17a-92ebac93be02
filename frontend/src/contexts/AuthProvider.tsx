import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { User } from 'oidc-client-ts';
import authService from '../services/auth';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  getAccessToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      // XSS防护说明：本流程通过authService获取token/session，token已最小化暴露于sessionStorage，仍需关注前端XSS风险，建议定期安全扫描。
      setIsLoading(true);
      try {
        if (!authService.isSSOEnabled()) {
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }
        const currentUser = await authService.getUser();
        const authenticated = await authService.isAuthenticated();
        setUser(currentUser);
        setIsAuthenticated(authenticated);
      } catch (error) {
        setUser(null);
        setIsAuthenticated(false);
        // 可选：console.error('Auth initialization failed:', error);
      } finally {
        setIsLoading(false);
      }
    };
    initAuth();
  }, []);

  const login = useCallback(async () => {
    await authService.login();
  }, []);

  const logout = useCallback(async () => {
    await authService.logout();
    setUser(null);
    setIsAuthenticated(false);
  }, []);

  const getAccessToken = useCallback(async () => {
    return await authService.getAccessToken();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    getAccessToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// useAuth: 兼容原有 AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// useSession: 提供 session/token 信息
export const useSession = () => {
  const { user, isAuthenticated, isLoading, getAccessToken } = useAuth();
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    const fetchToken = async () => {
      const token = await getAccessToken();
      if (mounted) setAccessToken(token);
    };
    fetchToken();
    return () => { mounted = false; };
  }, [getAccessToken, user, isAuthenticated]);

  return { user, accessToken, isAuthenticated, isLoading };
};

export default AuthContext;
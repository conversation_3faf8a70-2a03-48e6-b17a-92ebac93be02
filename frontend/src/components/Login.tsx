import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Container, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const { isAuthenticated, isLoading, login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 解析 redirect 参数
    const params = new URLSearchParams(location.search);
    // decode redirect，防止多重encode
    const redirect = params.get('redirect') ? decodeURIComponent(params.get('redirect')!) : '/';

    // 已认证，跳转目标页
    if (isAuthenticated && !isLoading) {
      navigate(redirect, { replace: true });
      return;
    }

    // 未认证且未在加载中，自动触发登录
    if (!isAuthenticated && !isLoading) {
      login().catch(() => {
        navigate('/error', { replace: true });
      });
    }
// [安全日志] SSO登录跳转流程未对redirect/callbackUrl参数做任何白名单或同源校验，存在open redirect及认证绕过高危风险。
  }, [isAuthenticated, isLoading, login, navigate, location.search]);

  // 只显示加载提示
  return (
    <Container className="d-flex justify-content-center align-items-center min-vh-100">
      <div className="text-center">
        <Spinner animation="border" role="status" className="mb-3" />
        <p>正在跳转认证...</p>
      </div>
    </Container>
  );
};

export default Login;

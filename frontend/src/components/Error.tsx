import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthProvider';

const ERROR_MAP: Record<string, { title: string; message: string; }> = {
  login_failed: {
    title: '登录失败',
    message: '登录过程出现问题，请重试。',
  },
  token_expired: {
    title: '登录已过期',
    message: '您的登录状态已失效，请重新登录。',
  },
  forbidden: {
    title: '权限不足',
    message: '您没有权限访问该页面。',
  },
  default: {
    title: '认证错误',
    message: '发生未知认证错误，请重试或联系管理员。',
  },
};

const Error: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { login } = useAuth();

  // 支持 URL 参数 ?type=xxx
  const params = new URLSearchParams(location.search);
  const type = params.get('type') || 'default';
  const errorInfo = ERROR_MAP[type] || ERROR_MAP.default;

  const handleRelogin = () => {
    login();
  };

  const handleHome = () => {
    window.location.href = '/';
  };

  return (
    <div style={{
      minHeight: '60vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
    }}>
      <h2 style={{ marginBottom: 16 }}>{errorInfo.title}</h2>
      <p style={{ marginBottom: 32, color: '#888' }}>{errorInfo.message}</p>
      <div style={{ display: 'flex', gap: 16 }}>
        <button
          onClick={handleRelogin}
          style={{
            padding: '8px 24px',
            background: '#1976d2',
            color: '#fff',
            border: 'none',
            borderRadius: 4,
            cursor: 'pointer',
            fontSize: 16,
          }}
        >
          重新登录
        </button>
        <button
          onClick={handleHome}
          style={{
            padding: '8px 24px',
            background: '#e0e0e0',
            color: '#333',
            border: 'none',
            borderRadius: 4,
            cursor: 'pointer',
            fontSize: 16,
          }}
        >
          返回首页
        </button>
      </div>
    </div>
  );
};

export default Error;
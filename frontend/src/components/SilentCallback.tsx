import React, { useEffect } from 'react';
import authService from '../services/auth';

const SilentCallback: React.FC = () => {
  useEffect(() => {
    const handleSilentCallback = async () => {
      try {
        // XSS防护说明：silent renew流程涉及token/session写入sessionStorage，若存在XSS漏洞将导致token批量泄露。
        // 建议：定期进行前端XSS安全扫描，确保所有输入点无DOM注入风险，优先使用httpOnly cookie方案。
        await authService.handleSilentCallback();
      } catch (error) {
        console.error('Silent callback failed:', error);
      }
    };

    handleSilentCallback();
  }, []);

  return (
    <div style={{ display: 'none' }}>
      Silent callback processing...
    </div>
// [安全日志] token及会话信息全部存储于localStorage，silent renew流程无隔离/加密/XSS防护，若存在XSS漏洞将导致token批量泄露。
  );
};

export default SilentCallback;

// 对话项类型
export interface DialogueItem {
  role: string;
  text: string;
}

// 文本内容类型
export interface TextContent {
  title: string;
  labels: {
    articleInput: string;
    generateScript: string;
    jsonInput: string;
    submit: string;
    loadExample: string;
  };
  placeholders: {
    jsonInput: string;
  };
}

// 角色声音映射类型
export interface VoiceRoleItem {
  key: string;
  desc: string;
}

// API响应类型
export interface ApiResponse {
  status: string;
  audio: string;
}

export interface ApiErrorResponse {
  error: string;
  status: string;
}
/**
 * AI 视频基础信息
 */
export interface AIVideoBasic {
  id: number;
  input: string;
  name: string;
  created_at: string;
}

/**
 * AI 视频详情
 */
/**
 * 图片素材项
 */
export interface ImageItem {
  id: number;
  url: string;
}

/**
 * 音频素材项
 */
export interface MaterialItem {
  id: number;
  text: string;
  audio_url: string;
}

export interface AIVideoDetailResponse extends AIVideoBasic {
  images?: ImageItem[]; // 图片素材数组
  texts?: string[] | undefined | null; // 生成文本（数组或空）
  image_prompts?: string[]; // 图片提示词
  materialList?: MaterialItem[]; // 音频素材列表
  draft_url?: string; // 草稿视频URL
  content?: string; // 生成内容
  debug_url?: string | undefined; // 调试用URL
  [key: string]: any; // 兼容后端扩展
}

/**
 * AI 视频分页响应
 */
export interface AIVideoListResponse {
  data: AIVideoBasic[];
  total: number;
  page: number;
  limit: number;
}
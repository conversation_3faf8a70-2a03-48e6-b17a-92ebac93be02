version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5008:5008"
    volumes:
      - ./backend/config.toml:/app/config.toml
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend-next
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=https://aiworkspace-api.01sworld.top:8443/api
        - NEXT_PUBLIC_TITLE=AI 工作台
        - NEXT_PUBLIC_SSO_ENABLED=true
        - AUTHELIA_WELL_KNOWN_URL=https://sso.01sworld.top:8443/.well-known/openid-configuration
        - AUTHELIA_CLIENT_ID=aiworkspace
        - AUTHELIA_CLIENT_SECRET=2KVyDSsbYveBRaN79bEFlZhs3q9fKFTKoyDSWCJlVQlHMquV_Uy7FVDeKKAtaiYTz1A4go3~
        - NEXTAUTH_URL=https://aiworkspace.01sworld.top:8443
        - NEXTAUTH_SECRET=KNG3NIETPDt+jXGUGjBBUmQtfMGPQxWGhz9Vqrgx2zY=
    ports:
      - "3008:3008"
    depends_on:
      - backend
    restart: unless-stopped
